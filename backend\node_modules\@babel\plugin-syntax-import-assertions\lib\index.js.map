{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "assertVersion", "isPlugin", "plugin", "name", "Array", "isArray", "options", "length", "manipulateOptions", "opts", "plugins", "i", "splice", "Object", "assign", "deprecatedAssertSyntax", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const isPlugin = (plugin: string | [string, object], name: string) =>\n    name === \"plugin\" || (Array.isArray(plugin) && plugin[0] === \"plugin\");\n  const options = (plugin: string | [string, object]) =>\n    Array.isArray(plugin) && plugin.length > 1 ? plugin[1] : {};\n\n  return {\n    name: \"syntax-import-assertions\",\n\n    manipulateOptions(opts, { plugins }) {\n      for (let i = 0; i < plugins.length; i++) {\n        const plugin = plugins[i];\n\n        if (isPlugin(plugin, \"deprecatedImportAssert\")) return;\n\n        if (isPlugin(plugin, \"importAttributes\")) {\n          plugins.splice(i, 1, \"deprecatedImportAssert\", [\n            \"importAttributes\",\n            { ...options(plugin), deprecatedAssertSyntax: true },\n          ]);\n          return;\n        }\n      }\n\n      plugins.push(\"importAssertions\");\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,QAAQ,GAAGA,CAACC,MAAiC,EAAEC,IAAY,KAC/DA,IAAI,KAAK,QAAQ,IAAKC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,QAAS;EACxE,MAAMI,OAAO,GAAIJ,MAAiC,IAChDE,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,IAAIA,MAAM,CAACK,MAAM,GAAG,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAE7D,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAEhCK,iBAAiBA,CAACC,IAAI,EAAE;MAAEC;IAAQ,CAAC,EAAE;MACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACH,MAAM,EAAEI,CAAC,EAAE,EAAE;QACvC,MAAMT,MAAM,GAAGQ,OAAO,CAACC,CAAC,CAAC;QAEzB,IAAIV,QAAQ,CAACC,MAAM,EAAE,wBAAwB,CAAC,EAAE;QAEhD,IAAID,QAAQ,CAACC,MAAM,EAAE,kBAAkB,CAAC,EAAE;UACxCQ,OAAO,CAACE,MAAM,CAACD,CAAC,EAAE,CAAC,EAAE,wBAAwB,EAAE,CAC7C,kBAAkB,EAAAE,MAAA,CAAAC,MAAA,KACbR,OAAO,CAACJ,MAAM,CAAC;YAAEa,sBAAsB,EAAE;UAAI,GACnD,CAAC;UACF;QACF;MACF;MAEAL,OAAO,CAACM,IAAI,CAAC,kBAAkB,CAAC;IAClC;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}